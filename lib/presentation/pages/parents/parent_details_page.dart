import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/parent.dart';
import '../../../domain/entities/student.dart';

/// Parent details page with responsive design
/// Following Single Responsibility Principle by focusing only on parent details display
class ParentDetailsPage extends StatefulWidget {
  const ParentDetailsPage({super.key});

  @override
  State<ParentDetailsPage> createState() => _ParentDetailsPageState();
}

class _ParentDetailsPageState extends State<ParentDetailsPage> {
  final ParentsController _controller = Get.find<ParentsController>();
  Parent? _parent;
  List<Student> _students = [];
  bool _isLoading = true;
  bool _isLoadingStudents = false;

  @override
  void initState() {
    super.initState();
    _loadParent();
  }

  Future<void> _loadParent() async {
    final parent = Get.arguments as Parent?;
    if (parent != null) {
      setState(() {
        _parent = parent;
        _isLoading = false;
      });
      await _loadStudents();
    } else {
      // If no parent passed, try to get from route parameters
      final parentId = Get.parameters['id'];
      if (parentId != null) {
        final fetchedParent = await _controller.getParentById(
          int.parse(parentId),
        );
        if (fetchedParent != null) {
          setState(() {
            _parent = fetchedParent;
            _isLoading = false;
          });
          await _loadStudents();
        } else {
          Get.back();
          Get.snackbar(
            'خطأ',
            'لم يتم العثور على ولي الأمر',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.back();
        Get.snackbar(
          'خطأ',
          'معرف ولي الأمر مطلوب',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  Future<void> _loadStudents() async {
    if (_parent?.id != null) {
      setState(() {
        _isLoadingStudents = true;
      });

      final students = await _controller.getStudentsByParentId(
        _parent!.id.toString(),
      );

      setState(() {
        _students = students;
        _isLoadingStudents = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        backgroundColor:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.backgroundDark
                : ColorConstants.background,
        appBar: _buildAppBar(context),
        body: _buildBody(context),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'تفاصيل ولي الأمر',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: ColorConstants.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: ColorConstants.white),
        onPressed: () => Get.back(),
      ),
      actions: [
        if (!_isLoading && _parent != null) ...[
          IconButton(
            icon: const Icon(Icons.edit, color: ColorConstants.white),
            onPressed:
                () => Get.toNamed(AppRoutes.editParent, arguments: _parent),
            tooltip: 'تعديل ولي الأمر',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: ColorConstants.white),
            onPressed: () {
              _loadParent();
              _loadStudents();
            },
            tooltip: 'تحديث البيانات',
          ),
        ],
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: LoadingWidget(message: 'جاري تحميل بيانات ولي الأمر...'),
      );
    }

    if (_parent == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على ولي الأمر',
              style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: ColorConstants.white,
              ),
              child: const Text('العودة'),
            ),
          ],
        ),
      );
    }

    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildParentHeader(),
          const SizedBox(height: 32),
          if (isMobile) ...[
            _buildBasicInfoCard(),
            const SizedBox(height: 16),
            _buildContactInfoCard(),
            const SizedBox(height: 16),
            _buildAdditionalInfoCard(),
            const SizedBox(height: 16),
            _buildChildrenCard(),
          ] else ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      _buildBasicInfoCard(),
                      const SizedBox(height: 16),
                      _buildContactInfoCard(),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      _buildAdditionalInfoCard(),
                      const SizedBox(height: 16),
                      _buildChildrenCard(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    if (_parent == null) return const SizedBox.shrink();

    return FloatingActionButton(
      onPressed: () => Get.toNamed(AppRoutes.editParent, arguments: _parent),
      backgroundColor: ColorConstants.primary,
      child: const Icon(Icons.edit, color: ColorConstants.white),
    );
  }

  Widget _buildParentHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorConstants.primary,
            ColorConstants.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: ColorConstants.white.withValues(alpha: 0.2),
            child: Text(
              _parent!.name?.isNotEmpty == true
                  ? _parent!.name![0].toUpperCase()
                  : 'P',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: ColorConstants.white,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _parent!.name ?? 'غير محدد',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorConstants.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color:
                  _parent!.status == 1
                      ? ColorConstants.success.withValues(alpha: 0.2)
                      : ColorConstants.error.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color:
                    _parent!.status == 1
                        ? ColorConstants.success
                        : ColorConstants.error,
                width: 1,
              ),
            ),
            child: Text(
              _parent!.status == 1 ? 'نشط' : 'غير نشط',
              style: TextStyle(
                color:
                    _parent!.status == 1
                        ? ColorConstants.success
                        : ColorConstants.error,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return _buildInfoCard(
      title: 'المعلومات الأساسية',
      icon: Icons.person,
      children: [
        _buildInfoRow('الاسم الكامل', _parent!.name ?? 'غير محدد'),
        _buildInfoRow('البريد الإلكتروني', _parent!.email ?? 'غير محدد'),
        _buildInfoRow('رقم الهاتف', _parent!.phone ?? 'غير محدد'),
        _buildInfoRow('الحالة', _parent!.status == 1 ? 'نشط' : 'غير نشط'),
      ],
    );
  }

  Widget _buildContactInfoCard() {
    return _buildInfoCard(
      title: 'معلومات الاتصال',
      icon: Icons.contact_phone,
      children: [
        _buildInfoRow('العنوان', _parent!.address ?? 'غير محدد'),
        _buildInfoRow('نوع المصادقة', _parent!.typeAuth ?? 'غير محدد'),
        _buildInfoRow(
          'حالة الاشتراك',
          _parent!.subscriptionStatus == true ? 'مفعل' : 'غير مفعل',
        ),
        _buildInfoRow(
          'تاريخ التحقق من البريد',
          _formatDate(_parent!.emailVerifiedAt),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoCard() {
    return _buildInfoCard(
      title: 'المعلومات الإضافية',
      icon: Icons.info,
      children: [
        _buildInfoRow('تاريخ الإنشاء', _formatDate(_parent!.createdAt)),
        _buildInfoRow('آخر تحديث', _formatDate(_parent!.updatedAt)),
        _buildInfoRow('عدد الأطفال', '${_parent!.childrenCount ?? 0}'),
        _buildInfoRow(
          'رمز Firebase',
          _parent!.firebaseToken != null ? 'متوفر' : 'غير متوفر',
        ),
      ],
    );
  }

  Widget _buildChildrenCard() {
    return _buildInfoCard(
      title: 'الأطفال المرتبطين',
      icon: Icons.child_care,
      children: [
        if (_isLoadingStudents) ...[
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: LoadingWidget(message: 'جاري تحميل بيانات الأطفال...'),
            ),
          ),
        ] else if (_students.isEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey.shade600, size: 20),
                const SizedBox(width: 12),
                Text(
                  'لا توجد أطفال مرتبطين بولي الأمر',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
          ),
        ] else ...[
          ..._students
              .map(
                (student) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ColorConstants.primary.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: ColorConstants.primary.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: ColorConstants.primary.withValues(
                          alpha: 0.2,
                        ),
                        child: Text(
                          student.name?.isNotEmpty == true
                              ? student.name![0].toUpperCase()
                              : 'S',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: ColorConstants.primary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              student.name ?? 'غير محدد',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                            if (student.schools?.isNotEmpty == true) ...[
                              const SizedBox(height: 2),
                              Text(
                                student.schools!,
                                style: TextStyle(
                                  color: ColorConstants.textSecondary,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed:
                            () => Get.toNamed(
                              AppRoutes.studentDetails,
                              arguments: student,
                            ),
                        icon: const Icon(Icons.visibility, size: 16),
                        tooltip: 'عرض تفاصيل الطالب',
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ],
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.surfaceDark
                : ColorConstants.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.textSecondaryDark.withValues(alpha: 0.3)
                  : ColorConstants.textSecondary.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: ColorConstants.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textSecondaryDark
                        : ColorConstants.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textPrimaryDark
                        : ColorConstants.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return 'غير محدد';
    }

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
