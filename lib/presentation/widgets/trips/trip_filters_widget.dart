import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/buses_controller.dart';

/// TripFiltersWidget for filtering trips
/// Following Single Responsibility Principle by focusing only on trip filtering
class TripFiltersWidget extends StatefulWidget {
  final Function(String) onBusFilterChanged;
  final Function(String) onDateFilterChanged;
  final Function(String) onSearchChanged;

  const TripFiltersWidget({
    super.key,
    required this.onBusFilterChanged,
    required this.onDateFilterChanged,
    required this.onSearchChanged,
  });

  @override
  State<TripFiltersWidget> createState() => _TripFiltersWidgetState();
}

class _TripFiltersWidgetState extends State<TripFiltersWidget> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  String _selectedBusId = '';

  @override
  void dispose() {
    _searchController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.filter_list_rounded,
                color: Get.theme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'تصفية الرحلات',
                style: Get.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Filters row
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              // Search filter
              SizedBox(width: 300, child: _buildSearchFilter()),

              // Bus filter
              SizedBox(width: 200, child: _buildBusFilter()),

              // Date filter
              SizedBox(width: 200, child: _buildDateFilter()),

              // Clear filters button
              _buildClearFiltersButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البحث',
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'البحث في الرحلات...',
            prefixIcon: Icon(Icons.search_rounded, color: Colors.grey[500]),
            suffixIcon:
                _searchController.text.isNotEmpty
                    ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        widget.onSearchChanged('');
                      },
                      icon: Icon(Icons.clear_rounded, color: Colors.grey[500]),
                    )
                    : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Get.theme.primaryColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          onChanged: widget.onSearchChanged,
        ),
      ],
    );
  }

  Widget _buildBusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الباص',
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        GetBuilder<BusesController>(
          builder: (busController) {
            return DropdownButtonFormField<String>(
              value: _selectedBusId.isEmpty ? null : _selectedBusId,
              decoration: InputDecoration(
                hintText: 'اختر الباص',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Get.theme.primaryColor),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: '',
                  child: Text('جميع الباصات'),
                ),
                ...busController.buses.map((bus) {
                  return DropdownMenuItem<String>(
                    value: bus.id?.toString() ?? '',
                    child: Text(bus.carNumber ?? 'غير محدد'),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedBusId = value ?? '';
                });
                widget.onBusFilterChanged(_selectedBusId);
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التاريخ',
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _dateController,
          decoration: InputDecoration(
            hintText: 'اختر التاريخ',
            prefixIcon: Icon(
              Icons.calendar_today_rounded,
              color: Colors.grey[500],
            ),
            suffixIcon:
                _dateController.text.isNotEmpty
                    ? IconButton(
                      onPressed: () {
                        _dateController.clear();
                        widget.onDateFilterChanged('');
                      },
                      icon: Icon(Icons.clear_rounded, color: Colors.grey[500]),
                    )
                    : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Get.theme.primaryColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          readOnly: true,
          onTap: () => _selectDate(),
        ),
      ],
    );
  }

  Widget _buildClearFiltersButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          ' ', // Empty space for alignment
          style: Get.textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        ElevatedButton.icon(
          onPressed: _clearAllFilters,
          icon: const Icon(Icons.clear_all_rounded),
          label: const Text('مسح الفلاتر'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[100],
            foregroundColor: Colors.grey[700],
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Colors.grey[300]!),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Get.theme.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final formattedDate =
          '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      _dateController.text = formattedDate;
      widget.onDateFilterChanged(formattedDate);
    }
  }

  void _clearAllFilters() {
    setState(() {
      _searchController.clear();
      _dateController.clear();
      _selectedBusId = '';
    });

    widget.onSearchChanged('');
    widget.onDateFilterChanged('');
    widget.onBusFilterChanged('');
  }
}
