import 'package:get/get.dart';
import '../controllers/trip_controller.dart';

/// TripBinding for dependency injection
/// Following Single Responsibility Principle by focusing only on trip dependencies
class TripBinding extends Bindings {
  @override
  void dependencies() {
    // Trip controller
    Get.lazyPut<TripController>(
      () => TripController(
        getCurrentTripUseCase: Get.find(),
        getRecentTripsUseCase: Get.find(),
        getTripByIdUseCase: Get.find(),
      ),
      fenix: true,
    );
  }
}
