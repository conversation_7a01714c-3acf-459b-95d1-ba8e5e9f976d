import 'package:get/get.dart';
import '../../data/repositories/parent_repository_impl.dart';
import '../../domain/repositories/parent_repository.dart';
import '../../domain/usecases/get_parents_usecase.dart';
import '../../domain/usecases/get_parent_by_id_usecase.dart';
import '../../domain/usecases/create_parent_usecase.dart';
import '../../domain/usecases/update_parent_usecase.dart';
import '../../domain/usecases/delete_parent_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../controllers/parents_controller.dart';
import '../../core/network/api_service.dart';

/// Parents binding for dependency injection
/// Following Single Responsibility Principle by focusing only on parents dependencies
class ParentsBinding extends Bindings {
  @override
  void dependencies() {
    // Repository
    Get.lazyPut<ParentRepository>(
      () => ParentRepositoryImpl(Get.find<ApiService>()),
    );

    // Use cases
    Get.lazyPut(() => GetParentsUseCase(Get.find<ParentRepository>()));
    Get.lazyPut(() => GetParentByIdUseCase(Get.find<ParentRepository>()));
    Get.lazyPut(() => CreateParentUseCase(Get.find<ParentRepository>()));
    Get.lazyPut(() => UpdateParentUseCase(Get.find<ParentRepository>()));
    Get.lazyPut(() => DeleteParentUseCase(Get.find<ParentRepository>()));
    Get.lazyPut(() => GetStudentsByParentIdUseCase(Get.find()));

    // Controller
    Get.lazyPut(
      () => ParentsController(
        getParentsUseCase: Get.find(),
        getParentByIdUseCase: Get.find(),
        createParentUseCase: Get.find(),
        updateParentUseCase: Get.find(),
        deleteParentUseCase: Get.find(),
        getStudentsByParentIdUseCase: Get.find(),
      ),
    );
  }
}
