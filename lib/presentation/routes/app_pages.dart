import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../bindings/auth_binding.dart';
import '../bindings/bus_binding.dart';
import '../bindings/dashboard_binding.dart';
import '../bindings/home_binding.dart';
import '../bindings/profile_binding.dart';
import '../bindings/students_binding.dart';
import '../bindings/trip_binding.dart';
import '../pages/auth/forgot_password_page.dart';
import '../pages/auth/login_page.dart';
import '../pages/auth/register_page.dart';
import '../pages/auth/reset_password_page.dart';
import '../pages/buses/add_bus_page.dart';
import '../pages/buses/buses_page.dart';
import '../pages/buses/edit_bus_page.dart';
import '../pages/dashboard/dashboard_page.dart';
import '../pages/home/<USER>';
import '../pages/language/language_selection_page.dart';
import '../pages/placeholder_page.dart';
import '../pages/profile/change_password_page.dart';
import '../pages/profile/edit_profile_page.dart';
import '../pages/profile/profile_page.dart';
import '../pages/settings/settings_page.dart';
import '../pages/students/students_page.dart';
import '../pages/students/add_student_page.dart';
import '../pages/students/edit_student_page.dart';
import '../pages/drivers/drivers_page.dart';
import '../pages/drivers/add_driver_page.dart';
import '../pages/drivers/edit_driver_page.dart';
import '../pages/drivers/driver_details_page.dart';
import '../pages/supervisors/supervisors_page.dart';
import '../pages/supervisors/add_supervisor_page.dart';
import '../pages/supervisors/edit_supervisor_page.dart';
import '../pages/supervisors/supervisor_details_page.dart';
import '../pages/trips/current_trips_page.dart';
import '../pages/trips/previous_trips_page.dart';
import '../pages/trips/trip_details_page.dart';
import '../pages/debug/bus_api_debug_page.dart';
import '../bindings/drivers_binding.dart';
import '../bindings/supervisors_binding.dart';
import 'app_routes.dart';

/// AppPages class for defining application routes
/// Following Single Responsibility Principle by focusing only on route definitions
class AppPages {
  // Private constructor to prevent instantiation
  AppPages._();

  static final routes = [
    // Main routes
    GetPage(
      name: AppRoutes.home,
      page: () => const DashboardPage(),
      binding: DashboardBinding(),
      transition: Transition.fadeIn,
    ),

    // Home page
    GetPage(
      name: AppRoutes.homePage,
      page: () => const HomePage(),
      binding: HomeBinding(),
      transition: Transition.fadeIn,
    ),

    // Auth routes
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginPage(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.register,
      page: () => const RegisterPage(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.forgotPassword,
      page: () => const ForgotPasswordPage(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.resetPassword,
      page: () => const ResetPasswordPage(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
    ),

    // Language selection
    GetPage(
      name: AppRoutes.languageSelection,
      page: () => const LanguageSelectionPage(),
      transition: Transition.fadeIn,
    ),

    // Profile and settings
    GetPage(
      name: AppRoutes.profile,
      page: () => const ProfilePage(),
      binding: ProfileBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.editProfile,
      page: () => const EditProfilePage(),
      binding: ProfileBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.changePassword,
      page: () => const ChangePasswordPage(),
      binding: ProfileBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsPage(),
      transition: Transition.fadeIn,
    ),

    // Student management
    GetPage(
      name: AppRoutes.students,
      page: () => const StudentsPage(),
      binding: StudentsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.addStudent,
      page: () => const AddStudentPage(),
      binding: StudentsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.editStudent,
      page: () => const EditStudentPage(),
      binding: StudentsBinding(),
      transition: Transition.fadeIn,
    ),

    // Bus management
    GetPage(
      name: AppRoutes.buses,
      page: () => const BusesPage(),
      binding: BusBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.addBus,
      page: () => const AddBusPage(),
      binding: BusBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.editBus,
      page: () => const EditBusPage(),
      binding: BusBinding(),
      transition: Transition.fadeIn,
    ),

    // Driver management
    GetPage(
      name: AppRoutes.drivers,
      page: () => const DriversPage(),
      binding: DriversBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.addDriver,
      page: () => const AddDriverPage(),
      binding: DriversBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.editDriver,
      page: () => const EditDriverPage(),
      binding: DriversBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.driverDetails,
      page: () => const DriverDetailsPage(),
      binding: DriversBinding(),
      transition: Transition.fadeIn,
    ),

    // Supervisor management
    GetPage(
      name: AppRoutes.supervisors,
      page: () => const SupervisorsPage(),
      binding: SupervisorsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.addSupervisor,
      page: () => const AddSupervisorPage(),
      binding: SupervisorsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.editSupervisor,
      page: () => const EditSupervisorPage(),
      binding: SupervisorsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.supervisorDetails,
      page: () => const SupervisorDetailsPage(),
      binding: SupervisorsBinding(),
      transition: Transition.fadeIn,
    ),

    // Trip management
    GetPage(
      name: AppRoutes.trips,
      page:
          () => PlaceholderPage(
            title: 'الرحلات',
            icon: Icons.map_rounded,
            description: 'صفحة إدارة الرحلات قيد الإنشاء',
          ),
      transition: Transition.fadeIn,
    ),

    // Attendance management
    GetPage(
      name: AppRoutes.attendance,
      page:
          () => PlaceholderPage(
            title: 'الحضور',
            icon: Icons.fact_check_rounded,
            description: 'صفحة إدارة الحضور قيد الإنشاء',
          ),
      transition: Transition.fadeIn,
    ),

    // Parent management
    GetPage(
      name: AppRoutes.parents,
      page:
          () => PlaceholderPage(
            title: 'أولياء الأمور',
            icon: Icons.family_restroom_rounded,
            description: 'صفحة إدارة أولياء الأمور قيد الإنشاء',
          ),
      transition: Transition.fadeIn,
    ),

    // Address change requests
    GetPage(
      name: AppRoutes.addressChangeRequests,
      page:
          () => PlaceholderPage(
            title: 'طلبات تغيير العنوان',
            icon: Icons.location_on_rounded,
            description: 'صفحة طلبات تغيير العنوان قيد الإنشاء',
          ),
      transition: Transition.fadeIn,
    ),

    // Previous trips
    GetPage(
      name: AppRoutes.previousTrips,
      page:
          () => PlaceholderPage(
            title: 'الرحلات السابقة',
            icon: Icons.history_rounded,
            description: 'صفحة الرحلات السابقة قيد الإنشاء',
          ),
      transition: Transition.fadeIn,
    ),

    // Current trips
    GetPage(
      name: AppRoutes.currentTrips,
      page: () => const CurrentTripsPage(),
      binding: TripBinding(),
      transition: Transition.fadeIn,
    ),

    // Previous trips
    GetPage(
      name: AppRoutes.previousTrips,
      page: () => const PreviousTripsPage(),
      binding: TripBinding(),
      transition: Transition.fadeIn,
    ),

    // Trip details
    GetPage(
      name: AppRoutes.previousTripDetails,
      page: () => const TripDetailsPage(),
      binding: TripBinding(),
      transition: Transition.fadeIn,
    ),

    GetPage(
      name: AppRoutes.currentTripDetails,
      page: () => const TripDetailsPage(),
      binding: TripBinding(),
      transition: Transition.fadeIn,
    ),

    // Track trip
    GetPage(
      name: AppRoutes.trackTrip,
      page:
          () => PlaceholderPage(
            title: 'تتبع الرحلة',
            icon: Icons.location_on_rounded,
            description: 'صفحة تتبع الرحلة الحالية قيد الإنشاء',
          ),
      transition: Transition.fadeIn,
    ),

    // Debug routes
    GetPage(
      name: AppRoutes.busApiDebug,
      page: () => const BusApiDebugPage(),
      transition: Transition.fadeIn,
    ),
  ];
}
