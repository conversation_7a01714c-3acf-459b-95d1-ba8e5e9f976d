import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/trip.dart';
import '../../domain/usecases/get_current_trip.dart';
import '../../domain/usecases/get_recent_trips.dart';
import '../../domain/usecases/get_trip_by_id.dart';

/// TripController for managing trip state
/// Following Single Responsibility Principle by focusing only on trip state management
class TripController extends GetxController {
  final GetCurrentTrip getCurrentTripUseCase;
  final GetRecentTripsUseCase getRecentTripsUseCase;
  final GetTripById getTripByIdUseCase;

  TripController({
    required this.getCurrentTripUseCase,
    required this.getRecentTripsUseCase,
    required this.getTripByIdUseCase,
  });

  // Observable state
  final _isLoading = false.obs;
  final _currentTrip = Rxn<Trip>();
  final _recentTrips = <Trip>[].obs;
  final _selectedTrip = Rxn<Trip>();
  final _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  Trip? get currentTrip => _currentTrip.value;
  List<Trip> get recentTrips => _recentTrips;
  Trip? get selectedTrip => _selectedTrip.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    LoggerService.info('TripController initialized');
    // Load current trip on initialization
    loadCurrentTrip();
  }

  /// Load current trip
  Future<void> loadCurrentTrip() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Loading current trip');

      final result = await getCurrentTripUseCase.call();

      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load current trip',
            error: failure.message,
          );
          _errorMessage.value = failure.message;
          _currentTrip.value = null;
        },
        (trip) {
          LoggerService.success('Current trip loaded successfully');
          _currentTrip.value = trip;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading current trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
      _currentTrip.value = null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load recent trips with pagination
  Future<void> loadRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
    bool clearExisting = true,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (clearExisting) {
        _recentTrips.clear();
      }

      LoggerService.info(
        'Loading recent trips',
        data: {
          'page': page,
          'limit': limit,
          'busId': busId,
          'date': date,
          'search': search,
        },
      );

      final result = await getRecentTripsUseCase.call(
        page: page,
        limit: limit,
        busId: busId,
        date: date,
        search: search,
      );

      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load recent trips',
            error: failure.message,
          );
          _errorMessage.value = failure.message;
        },
        (trips) {
          LoggerService.success(
            'Recent trips loaded successfully',
            data: {'count': trips.length},
          );

          if (clearExisting) {
            _recentTrips.assignAll(trips);
          } else {
            _recentTrips.addAll(trips);
          }
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading recent trips', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load trip by ID
  Future<void> loadTripById(String tripId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Loading trip by ID', data: {'tripId': tripId});

      final result = await getTripByIdUseCase.call(tripId);

      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load trip by ID',
            error: failure.message,
          );
          _errorMessage.value = failure.message;
          _selectedTrip.value = null;
        },
        (trip) {
          LoggerService.success(
            'Trip loaded successfully',
            data: {'tripId': tripId},
          );
          _selectedTrip.value = trip;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading trip by ID', error: e);
      _errorMessage.value = 'Unexpected error occurred';
      _selectedTrip.value = null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh current trip
  Future<void> refreshCurrentTrip() async {
    LoggerService.info('Refreshing current trip');
    await loadCurrentTrip();
  }

  /// Refresh recent trips
  Future<void> refreshRecentTrips() async {
    LoggerService.info('Refreshing recent trips');
    await loadRecentTrips(clearExisting: true);
  }

  /// Clear selected trip
  void clearSelectedTrip() {
    LoggerService.info('Clearing selected trip');
    _selectedTrip.value = null;
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Filter recent trips by bus
  Future<void> filterTripsByBus(String busId) async {
    LoggerService.info('Filtering trips by bus', data: {'busId': busId});
    await loadRecentTrips(busId: busId, clearExisting: true);
  }

  /// Filter recent trips by date
  Future<void> filterTripsByDate(String date) async {
    LoggerService.info('Filtering trips by date', data: {'date': date});
    await loadRecentTrips(date: date, clearExisting: true);
  }

  /// Search trips
  Future<void> searchTrips(String query) async {
    LoggerService.info('Searching trips', data: {'query': query});
    await loadRecentTrips(search: query, clearExisting: true);
  }

  /// Load more recent trips (pagination)
  Future<void> loadMoreRecentTrips({
    int page = 2,
    String? busId,
    String? date,
    String? search,
  }) async {
    LoggerService.info('Loading more recent trips', data: {'page': page});
    await loadRecentTrips(
      page: page,
      busId: busId,
      date: date,
      search: search,
      clearExisting: false,
    );
  }

  @override
  void onClose() {
    LoggerService.info('TripController disposed');
    super.onClose();
  }
}
