import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/trip.dart';
import '../../domain/usecases/get_current_trip.dart';
import '../../domain/usecases/get_recent_trips.dart';
import '../../domain/usecases/get_trip_by_id.dart';
import '../../domain/usecases/trip/create_trip_usecase.dart';
import '../../domain/usecases/trip/update_trip_usecase.dart';
import '../../domain/usecases/trip/delete_trip_usecase.dart';
import '../../domain/usecases/trip/start_trip_usecase.dart';
import '../../domain/usecases/trip/end_trip_usecase.dart';
import '../../domain/usecases/trip/get_trip_tracking_usecase.dart';
import '../../domain/usecases/trip/get_trips_usecase.dart';

/// Enhanced TripController for managing trip state and operations
/// Following Single Responsibility Principle by focusing only on trip state management
class TripController extends GetxController {
  final GetCurrentTrip getCurrentTripUseCase;
  final GetRecentTripsUseCase getRecentTripsUseCase;
  final GetTripById getTripByIdUseCase;
  final CreateTripUseCase createTripUseCase;
  final UpdateTripUseCase updateTripUseCase;
  final DeleteTripUseCase deleteTripUseCase;
  final StartTripUseCase startTripUseCase;
  final EndTripUseCase endTripUseCase;
  final GetTripTrackingUseCase getTripTrackingUseCase;
  final GetTripsUseCase getTripsUseCase;

  TripController({
    required this.getCurrentTripUseCase,
    required this.getRecentTripsUseCase,
    required this.getTripByIdUseCase,
    required this.createTripUseCase,
    required this.updateTripUseCase,
    required this.deleteTripUseCase,
    required this.startTripUseCase,
    required this.endTripUseCase,
    required this.getTripTrackingUseCase,
    required this.getTripsUseCase,
  });

  // Observable state
  final _isLoading = false.obs;
  final _isLoadingMore = false.obs;
  final _currentTrip = Rxn<Trip>();
  final _recentTrips = <Trip>[].obs;
  final _trips = <Trip>[].obs;
  final _selectedTrip = Rxn<Trip>();
  final _errorMessage = ''.obs;
  final _totalTrips = 0.obs;
  final _currentPage = 1.obs;
  final _hasMoreTrips = true.obs;

  // Filter state
  final _searchQuery = ''.obs;
  final _statusFilter = Rxn<String>();
  final _busIdFilter = Rxn<String>();
  final _driverIdFilter = Rxn<String>();
  final _supervisorIdFilter = Rxn<String>();
  final _startDateFilter = Rxn<DateTime>();
  final _endDateFilter = Rxn<DateTime>();

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  Trip? get currentTrip => _currentTrip.value;
  List<Trip> get recentTrips => _recentTrips;
  List<Trip> get trips => _trips;
  Trip? get selectedTrip => _selectedTrip.value;
  String get errorMessage => _errorMessage.value;
  int get totalTrips => _totalTrips.value;
  int get currentPage => _currentPage.value;
  bool get hasMoreTrips => _hasMoreTrips.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    LoggerService.info('TripController initialized');
    // Load current trip on initialization
    loadCurrentTrip();
  }

  /// Load current trip
  Future<void> loadCurrentTrip() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Loading current trip');

      final result = await getCurrentTripUseCase.call();

      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load current trip',
            error: failure.message,
          );
          _errorMessage.value = failure.message;
          _currentTrip.value = null;
        },
        (trip) {
          LoggerService.success('Current trip loaded successfully');
          _currentTrip.value = trip;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading current trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
      _currentTrip.value = null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load recent trips with pagination
  Future<void> loadRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
    bool clearExisting = true,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      if (clearExisting) {
        _recentTrips.clear();
      }

      LoggerService.info(
        'Loading recent trips',
        data: {
          'page': page,
          'limit': limit,
          'busId': busId,
          'date': date,
          'search': search,
        },
      );

      final result = await getRecentTripsUseCase.call(
        page: page,
        limit: limit,
        busId: busId,
        date: date,
        search: search,
      );

      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load recent trips',
            error: failure.message,
          );
          _errorMessage.value = failure.message;
        },
        (trips) {
          LoggerService.success(
            'Recent trips loaded successfully',
            data: {'count': trips.length},
          );

          if (clearExisting) {
            _recentTrips.assignAll(trips);
          } else {
            _recentTrips.addAll(trips);
          }
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading recent trips', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load trip by ID
  Future<void> loadTripById(String tripId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Loading trip by ID', data: {'tripId': tripId});

      final result = await getTripByIdUseCase.call(tripId);

      result.fold(
        (failure) {
          LoggerService.error(
            'Failed to load trip by ID',
            error: failure.message,
          );
          _errorMessage.value = failure.message;
          _selectedTrip.value = null;
        },
        (trip) {
          LoggerService.success(
            'Trip loaded successfully',
            data: {'tripId': tripId},
          );
          _selectedTrip.value = trip;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading trip by ID', error: e);
      _errorMessage.value = 'Unexpected error occurred';
      _selectedTrip.value = null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh current trip
  Future<void> refreshCurrentTrip() async {
    LoggerService.info('Refreshing current trip');
    await loadCurrentTrip();
  }

  /// Refresh recent trips
  Future<void> refreshRecentTrips() async {
    LoggerService.info('Refreshing recent trips');
    await loadRecentTrips(clearExisting: true);
  }

  /// Clear selected trip
  void clearSelectedTrip() {
    LoggerService.info('Clearing selected trip');
    _selectedTrip.value = null;
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Filter recent trips by bus
  Future<void> filterTripsByBus(String busId) async {
    LoggerService.info('Filtering trips by bus', data: {'busId': busId});
    await loadRecentTrips(busId: busId, clearExisting: true);
  }

  /// Filter recent trips by date
  Future<void> filterTripsByDate(String date) async {
    LoggerService.info('Filtering trips by date', data: {'date': date});
    await loadRecentTrips(date: date, clearExisting: true);
  }

  /// Load more recent trips (pagination)
  Future<void> loadMoreRecentTrips({
    int page = 2,
    String? busId,
    String? date,
    String? search,
  }) async {
    LoggerService.info('Loading more recent trips', data: {'page': page});
    await loadRecentTrips(
      page: page,
      busId: busId,
      date: date,
      search: search,
      clearExisting: false,
    );
  }

  // Enhanced trip management methods

  /// Load trips with filtering and pagination
  Future<void> loadTrips({bool clearExisting = true}) async {
    try {
      if (clearExisting) {
        _isLoading.value = true;
        _trips.clear();
        _currentPage.value = 1;
      } else {
        _isLoadingMore.value = true;
      }
      _errorMessage.value = '';

      LoggerService.info('Loading trips with filters');

      final result = await getTripsUseCase.call(
        page: _currentPage.value,
        limit: 10,
        search: _searchQuery.value.isEmpty ? null : _searchQuery.value,
        status: _statusFilter.value,
        busId: _busIdFilter.value,
        driverId: _driverIdFilter.value,
        supervisorId: _supervisorIdFilter.value,
        startDate: _startDateFilter.value,
        endDate: _endDateFilter.value,
      );

      result.fold(
        (failure) {
          LoggerService.error('Failed to load trips', error: failure.message);
          _errorMessage.value = failure.message;
        },
        (trips) {
          LoggerService.success(
            'Trips loaded successfully',
            data: {'count': trips.length},
          );

          if (clearExisting) {
            _trips.assignAll(trips);
          } else {
            _trips.addAll(trips);
          }

          _hasMoreTrips.value = trips.length >= 10;
          _totalTrips.value = _trips.length;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading trips', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
      _isLoadingMore.value = false;
    }
  }

  /// Load more trips (pagination)
  Future<void> loadMoreTrips() async {
    if (!_hasMoreTrips.value || _isLoadingMore.value) return;

    _currentPage.value++;
    await loadTrips(clearExisting: false);
  }

  /// Search trips
  Future<void> searchTrips(String query) async {
    _searchQuery.value = query;
    await loadTrips();
  }

  /// Apply filters
  Future<void> applyFilters({
    String? status,
    String? busId,
    String? driverId,
    String? supervisorId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _statusFilter.value = status;
    _busIdFilter.value = busId;
    _driverIdFilter.value = driverId;
    _supervisorIdFilter.value = supervisorId;
    _startDateFilter.value = startDate;
    _endDateFilter.value = endDate;
    await loadTrips();
  }

  /// Clear filters
  Future<void> clearFilters() async {
    _searchQuery.value = '';
    _statusFilter.value = null;
    _busIdFilter.value = null;
    _driverIdFilter.value = null;
    _supervisorIdFilter.value = null;
    _startDateFilter.value = null;
    _endDateFilter.value = null;
    await loadTrips();
  }

  /// Create trip
  Future<void> createTrip(Trip trip) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Creating trip');

      final result = await createTripUseCase.call(trip);

      result.fold(
        (failure) {
          LoggerService.error('Failed to create trip', error: failure.message);
          _errorMessage.value = failure.message;
        },
        (createdTrip) {
          LoggerService.success('Trip created successfully');
          _trips.insert(0, createdTrip);
          _totalTrips.value++;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error creating trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update trip
  Future<void> updateTrip(Trip trip) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Updating trip');

      final result = await updateTripUseCase.call(trip);

      result.fold(
        (failure) {
          LoggerService.error('Failed to update trip', error: failure.message);
          _errorMessage.value = failure.message;
        },
        (updatedTrip) {
          LoggerService.success('Trip updated successfully');
          final index = _trips.indexWhere((t) => t.id == updatedTrip.id);
          if (index != -1) {
            _trips[index] = updatedTrip;
          }
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error updating trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete trip
  Future<void> deleteTrip(String tripId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Deleting trip');

      final result = await deleteTripUseCase.call(tripId);

      result.fold(
        (failure) {
          LoggerService.error('Failed to delete trip', error: failure.message);
          _errorMessage.value = failure.message;
        },
        (success) {
          LoggerService.success('Trip deleted successfully');
          _trips.removeWhere((trip) => trip.id == tripId);
          _totalTrips.value--;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error deleting trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Start trip
  Future<void> startTrip(String tripId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Starting trip');

      final result = await startTripUseCase.call(tripId);

      result.fold(
        (failure) {
          LoggerService.error('Failed to start trip', error: failure.message);
          _errorMessage.value = failure.message;
        },
        (startedTrip) {
          LoggerService.success('Trip started successfully');
          final index = _trips.indexWhere((t) => t.id == startedTrip.id);
          if (index != -1) {
            _trips[index] = startedTrip;
          }
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error starting trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  /// End trip
  Future<void> endTrip(String tripId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Ending trip');

      final result = await endTripUseCase.call(tripId);

      result.fold(
        (failure) {
          LoggerService.error('Failed to end trip', error: failure.message);
          _errorMessage.value = failure.message;
        },
        (endedTrip) {
          LoggerService.success('Trip ended successfully');
          final index = _trips.indexWhere((t) => t.id == endedTrip.id);
          if (index != -1) {
            _trips[index] = endedTrip;
          }
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error ending trip', error: e);
      _errorMessage.value = 'Unexpected error occurred';
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void onClose() {
    LoggerService.info('TripController disposed');
    super.onClose();
  }
}
