import '../../core/constants/app_constants.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../models/trip_model.dart';

/// TripRemoteDataSource interface
/// Following Dependency Inversion Principle
abstract class TripRemoteDataSource {
  /// Get current trip
  Future<TripModel?> getCurrentTrip();

  /// Get trip by id
  Future<TripModel> getTripById(String id);

  /// Get upcoming trips
  Future<List<TripModel>> getUpcomingTrips();

  /// Get recent trips with pagination
  Future<List<TripModel>> getRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  });

  /// Get trip route details
  Future<Map<String, dynamic>> getTripRoute(String tripId);

  /// Get trip attendance (present students)
  Future<List<Map<String, dynamic>>> getTripAttendance(String tripId);

  /// Get trip absent students
  Future<List<Map<String, dynamic>>> getTripAbsentStudents(String tripId);
}

/// TripRemoteDataSourceImpl implementation
/// Following Single Responsibility Principle by focusing only on trip data operations
class TripRemoteDataSourceImpl implements TripRemoteDataSource {
  final ApiService apiService;

  TripRemoteDataSourceImpl({required this.apiService});

  @override
  Future<TripModel?> getCurrentTrip() async {
    try {
      LoggerService.info('Calling API for current trip');

      final response = await apiService.get(
        AppConstants.schoolCurrentTrips,
        isAuth: true,
      );

      LoggerService.debug('Current trip API response', data: response.data);

      // Handle different response formats from the original API
      if (response.data['status'] == true && response.data['data'] != null) {
        final tripData = response.data['data'];

        // Check if we have trip data
        if (tripData is List && tripData.isNotEmpty) {
          // Take the first active trip
          final firstTrip = tripData.first;
          return _mapCurrentTripToTripModel(firstTrip);
        } else if (tripData is Map<String, dynamic>) {
          return _mapCurrentTripToTripModel(tripData);
        }
      }

      // No current trip
      return null;
    } catch (e) {
      LoggerService.error('Failed to get current trip', error: e);
      rethrow;
    }
  }

  @override
  Future<TripModel> getTripById(String id) async {
    try {
      LoggerService.info('Calling API for trip by id: $id');

      final response = await apiService.get(
        '${AppConstants.tripEndpoint}/$id',
        isAuth: true,
      );

      LoggerService.debug('Trip by id API response', data: response.data);

      if (response.data['data'] != null) {
        return _mapCurrentTripToTripModel(response.data['data']);
      }

      throw Exception('Trip not found');
    } catch (e) {
      LoggerService.error('Failed to get trip by id', error: e);
      rethrow;
    }
  }

  @override
  Future<List<TripModel>> getUpcomingTrips() async {
    try {
      LoggerService.info('Calling API for upcoming trips');

      final response = await apiService.get(
        AppConstants.tripEndpoint,
        isAuth: true,
      );

      LoggerService.debug('Upcoming trips API response', data: response.data);

      if (response.data['data'] != null) {
        final tripsData = response.data['data'] as List;
        return tripsData
            .map((tripData) => _mapCurrentTripToTripModel(tripData))
            .toList();
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get upcoming trips', error: e);
      rethrow;
    }
  }

  @override
  Future<List<TripModel>> getRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  }) async {
    try {
      LoggerService.info(
        'Calling API for recent trips',
        data: {
          'page': page,
          'limit': limit,
          'busId': busId,
          'date': date,
          'search': search,
        },
      );

      // Build query parameters
      Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (busId != null && busId.isNotEmpty && busId != "0") {
        queryParams['bus_id'] = busId;
      }

      if (date != null && date.isNotEmpty) {
        queryParams['date'] = date;
      }

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final response = await apiService.get(
        AppConstants.schoolPreviousTrips,
        queryParameters: queryParams,
        isAuth: true,
      );

      LoggerService.debug('Recent trips API response', data: response.data);

      // Handle different response formats from the original API
      List<TripModel> trips = [];

      if (response.data['previous_trips'] != null) {
        final tripsData = response.data['previous_trips']['data'] as List;
        trips =
            tripsData
                .map((tripData) => _mapPreviousTripToTripModel(tripData))
                .toList();
      } else if (response.data['data'] != null) {
        final tripsData = response.data['data'] as List;
        trips =
            tripsData
                .map((tripData) => _mapPreviousTripToTripModel(tripData))
                .toList();
      }

      return trips;
    } catch (e) {
      LoggerService.error('Failed to get recent trips', error: e);
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getTripRoute(String tripId) async {
    try {
      LoggerService.info('Calling API for trip route: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripRouteDetails}$tripId',
        isAuth: true,
      );

      LoggerService.debug('Trip route API response', data: response.data);

      return response.data;
    } catch (e) {
      LoggerService.error('Failed to get trip route', error: e);
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getTripAttendance(String tripId) async {
    try {
      LoggerService.info('Calling API for trip attendance: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripAttendantStudents}$tripId',
        isAuth: true,
      );

      LoggerService.debug('Trip attendance API response', data: response.data);

      if (response.data['data'] != null) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get trip attendance', error: e);
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getTripAbsentStudents(
    String tripId,
  ) async {
    try {
      LoggerService.info('Calling API for trip absent students: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripAbsentStudents}$tripId',
        isAuth: true,
      );

      LoggerService.debug(
        'Trip absent students API response',
        data: response.data,
      );

      if (response.data['data'] != null) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get trip absent students', error: e);
      rethrow;
    }
  }

  /// Map current trip API response to TripModel
  TripModel _mapCurrentTripToTripModel(Map<String, dynamic> data) {
    return TripModel(
      id: data['id']?.toString() ?? '',
      name: data['name'] ?? 'Current Trip',
      schoolId: data['school_id']?.toString() ?? '',
      schoolName: data['school_name'] ?? '',
      busId: data['bus_id']?.toString() ?? '',
      busNumber: data['bus']?['car_number'] ?? data['car_number'] ?? '',
      driverId: data['driver_id']?.toString() ?? '',
      driverName: data['driver_name'] ?? '',
      supervisorId:
          data['supervisor_id']?.toString() ??
          data['userable_id']?.toString() ??
          '',
      supervisorName: data['supervisor_name'] ?? data['name'] ?? '',
      startTime:
          data['created_at'] != null
              ? DateTime.parse(data['created_at'])
              : DateTime.now(),
      endTime: data['end_at'] != null ? DateTime.parse(data['end_at']) : null,
      status: data['status']?.toString() ?? 'active',
      studentIds: [], // Will be populated from separate API calls
      attendedStudentIds: [], // Will be populated from separate API calls
      startLocation: data['address'] ?? '',
      endLocation: data['address'] ?? '',
      distance: 0.0, // Not provided in current API
      duration: 0, // Not provided in current API
      stops: [], // Will be populated from route API
      events: [], // Will be populated from route API
    );
  }

  /// Map previous trip API response to TripModel
  TripModel _mapPreviousTripToTripModel(Map<String, dynamic> data) {
    return TripModel(
      id: data['id']?.toString() ?? '',
      name: data['trip_type'] ?? 'Previous Trip',
      schoolId: data['school_id']?.toString() ?? '',
      schoolName: data['school_name'] ?? '',
      busId: data['bus_id']?.toString() ?? '',
      busNumber: data['bus']?['car_number'] ?? '',
      driverId: data['driver_id']?.toString() ?? '',
      driverName: data['driver_name'] ?? '',
      supervisorId: data['supervisor_id']?.toString() ?? '',
      supervisorName: data['supervisor_name'] ?? '',
      startTime:
          data['trips_date'] != null
              ? DateTime.parse(data['trips_date'])
              : DateTime.now(),
      endTime: data['end_at'] != null ? DateTime.parse(data['end_at']) : null,
      status: data['status']?.toString() ?? 'completed',
      studentIds: [], // Will be populated from separate API calls
      attendedStudentIds: [], // Will be populated from separate API calls
      startLocation: '',
      endLocation: '',
      distance: 0.0,
      duration: 0,
      stops: [], // Will be populated from route API
      events: [], // Will be populated from route API
    );
  }
}
