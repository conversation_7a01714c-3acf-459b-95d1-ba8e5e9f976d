import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/datasources/ad_remote_data_source.dart';
import '../../data/datasources/ad_remote_data_source_impl.dart';
import '../../data/datasources/dashboard_remote_data_source.dart';
import '../../data/datasources/dashboard_remote_data_source_impl.dart';
import '../../data/datasources/trip_remote_data_source.dart';
import '../../data/datasources/user_local_data_source.dart';
import '../../data/datasources/user_local_data_source_impl.dart';
import '../../data/datasources/user_remote_data_source.dart';
import '../../data/datasources/user_remote_data_source_impl.dart';
import '../../data/repositories/ad_repository_impl.dart';
import '../../data/repositories/dashboard_repository_impl.dart';
import '../../data/repositories/supervisor_repository_impl.dart';
import '../../data/repositories/parent_repository_impl.dart';
import '../../data/repositories/trip_repository_impl.dart';
import '../../data/repositories/user_repository_impl.dart';
import '../../domain/repositories/ad_repository.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/repositories/supervisor_repository.dart';
import '../../domain/repositories/parent_repository.dart';
import '../../domain/repositories/trip_repository.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/usecases/auth/change_password.dart';
import '../../domain/usecases/auth/forgot_password.dart';
import '../../domain/usecases/auth/get_current_user.dart';
import '../../domain/usecases/auth/is_authenticated.dart';
import '../../domain/usecases/auth/login_user.dart';
import '../../domain/usecases/auth/logout_user.dart';
import '../../domain/usecases/auth/register_user.dart';
import '../../domain/usecases/auth/reset_password.dart';
import '../../domain/usecases/dashboard/get_dashboard_stats.dart';
import '../../domain/usecases/dashboard/get_recent_trips.dart';
import '../../domain/usecases/dashboard/get_school_stats.dart';
import '../../domain/usecases/dashboard/get_upcoming_trips.dart';
import '../../domain/usecases/get_ads.dart';
import '../../domain/usecases/get_current_trip.dart';
import '../../domain/usecases/get_trip_by_id.dart';
import '../../domain/usecases/get_recent_trips.dart';
import '../../domain/usecases/get_upcoming_trips.dart';
import '../../domain/usecases/trip/create_trip_usecase.dart';
import '../../domain/usecases/trip/update_trip_usecase.dart';
import '../../domain/usecases/trip/delete_trip_usecase.dart';
import '../../domain/usecases/trip/start_trip_usecase.dart';
import '../../domain/usecases/trip/end_trip_usecase.dart';
import '../../domain/usecases/trip/get_trip_tracking_usecase.dart';
import '../../domain/usecases/trip/get_trips_usecase.dart';

import '../../domain/usecases/get_profile.dart';
import '../../domain/usecases/get_supervisors_usecase.dart';
import '../../domain/usecases/get_supervisor_by_id_usecase.dart';
import '../../domain/usecases/create_supervisor_usecase.dart';
import '../../domain/usecases/update_supervisor_usecase.dart';
import '../../domain/usecases/delete_supervisor_usecase.dart';
import '../../domain/usecases/get_parents_usecase.dart';
import '../../domain/usecases/get_parent_by_id_usecase.dart';
import '../../domain/usecases/create_parent_usecase.dart';
import '../../domain/usecases/update_parent_usecase.dart';
import '../../domain/usecases/delete_parent_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../../presentation/controllers/auth_controller.dart';
import '../../presentation/controllers/dashboard_controller.dart';
import '../../presentation/controllers/home_controller.dart';
import '../../presentation/controllers/supervisors_controller.dart';
import '../../presentation/controllers/parents_controller.dart';
import '../../presentation/controllers/trip_controller.dart';
import '../network/api_service.dart';

/// DependencyInjection class for setting up dependencies
/// Following Dependency Inversion Principle by providing abstractions
class DependencyInjection {
  // Private constructor to prevent instantiation
  DependencyInjection._();

  /// Initialize dependencies
  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    Get.put<SharedPreferences>(sharedPreferences, permanent: true);

    // Core services
    final apiService = await ApiService().init();
    Get.put<ApiService>(apiService, permanent: true);

    // Data sources
    // First register UserLocalDataSource
    Get.lazyPut<UserLocalDataSource>(
      () => UserLocalDataSourceImpl(
        sharedPreferences: Get.find<SharedPreferences>(),
      ),
      fenix: true,
    );

    // Then register UserRemoteDataSource with UserLocalDataSource dependency
    Get.lazyPut<UserRemoteDataSource>(
      () => UserRemoteDataSourceImpl(
        Get.find<ApiService>(),
        localDataSource: Get.find<UserLocalDataSource>(),
      ),
      fenix: true,
    );

    // Repositories
    Get.lazyPut<UserRepository>(
      () => UserRepositoryImpl(
        remoteDataSource: Get.find<UserRemoteDataSource>(),
        localDataSource: Get.find<UserLocalDataSource>(),
      ),
      fenix: true,
    );

    // Dashboard data sources
    Get.lazyPut<DashboardRemoteDataSource>(
      () => DashboardRemoteDataSourceImpl(apiService: Get.find<ApiService>()),
      fenix: true,
    );

    // Dashboard repository
    Get.lazyPut<DashboardRepository>(
      () => DashboardRepositoryImpl(
        remoteDataSource: Get.find<DashboardRemoteDataSource>(),
      ),
      fenix: true,
    );

    // Ad data sources
    Get.lazyPut<AdRemoteDataSource>(
      () => AdRemoteDataSourceImpl(apiService: Get.find<ApiService>()),
      fenix: true,
    );

    // Ad repository
    Get.lazyPut<AdRepository>(
      () => AdRepositoryImpl(remoteDataSource: Get.find<AdRemoteDataSource>()),
      fenix: true,
    );

    // Trip data sources
    Get.lazyPut<TripRemoteDataSource>(
      () => TripRemoteDataSourceImpl(apiService: Get.find<ApiService>()),
      fenix: true,
    );

    // Trip repository
    Get.lazyPut<TripRepository>(
      () => TripRepositoryImpl(
        remoteDataSource: Get.find<TripRemoteDataSource>(),
      ),
      fenix: true,
    );

    // Supervisor repository
    Get.lazyPut<SupervisorRepository>(
      () => SupervisorRepositoryImpl(apiService: Get.find<ApiService>()),
      fenix: true,
    );

    // Parent repository
    Get.lazyPut<ParentRepository>(
      () => ParentRepositoryImpl(Get.find<ApiService>()),
      fenix: true,
    );

    // Auth use cases
    Get.lazyPut(() => GetCurrentUser(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => LoginUser(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => LogoutUser(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => RegisterUser(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => ForgotPassword(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => ResetPassword(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => IsAuthenticated(Get.find<UserRepository>()), fenix: true);
    Get.lazyPut(() => ChangePassword(Get.find<UserRepository>()), fenix: true);

    // Dashboard use cases
    Get.lazyPut(
      () => GetDashboardStats(Get.find<DashboardRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => GetSchoolStats(Get.find<DashboardRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => GetRecentTrips(Get.find<DashboardRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => GetUpcomingTrips(Get.find<DashboardRepository>()),
      fenix: true,
    );

    // Ad use cases
    Get.lazyPut(() => GetAds(Get.find<AdRepository>()), fenix: true);

    // Trip use cases
    Get.lazyPut(() => GetCurrentTrip(Get.find<TripRepository>()), fenix: true);
    Get.lazyPut(() => GetTripById(Get.find<TripRepository>()), fenix: true);
    Get.lazyPut(
      () => GetRecentTripsUseCase(Get.find<TripRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => GetUpcomingTripsUseCase(Get.find<TripRepository>()),
      fenix: true,
    );

    // Enhanced trip use cases
    Get.lazyPut(
      () => CreateTripUseCase(Get.find<TripRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => UpdateTripUseCase(Get.find<TripRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => DeleteTripUseCase(Get.find<TripRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => StartTripUseCase(Get.find<TripRepository>()),
      fenix: true,
    );
    Get.lazyPut(() => EndTripUseCase(Get.find<TripRepository>()), fenix: true);
    Get.lazyPut(
      () => GetTripTrackingUseCase(Get.find<TripRepository>()),
      fenix: true,
    );
    Get.lazyPut(() => GetTripsUseCase(Get.find<TripRepository>()), fenix: true);

    // User use cases
    Get.lazyPut(() => GetProfile(Get.find<UserRepository>()), fenix: true);

    // Supervisor use cases
    Get.lazyPut(
      () => GetSupervisorsUseCase(Get.find<SupervisorRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => GetSupervisorByIdUseCase(Get.find<SupervisorRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => CreateSupervisorUseCase(Get.find<SupervisorRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => UpdateSupervisorUseCase(Get.find<SupervisorRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => DeleteSupervisorUseCase(Get.find<SupervisorRepository>()),
      fenix: true,
    );

    // Parent use cases
    Get.lazyPut(
      () => GetParentsUseCase(Get.find<ParentRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => GetParentByIdUseCase(Get.find<ParentRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => CreateParentUseCase(Get.find<ParentRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => UpdateParentUseCase(Get.find<ParentRepository>()),
      fenix: true,
    );
    Get.lazyPut(
      () => DeleteParentUseCase(Get.find<ParentRepository>()),
      fenix: true,
    );

    // Controllers
    Get.lazyPut(
      () => AuthController(
        getCurrentUserUseCase: Get.find<GetCurrentUser>(),
        loginUserUseCase: Get.find<LoginUser>(),
        logoutUserUseCase: Get.find<LogoutUser>(),
        registerUserUseCase: Get.find<RegisterUser>(),
        forgotPasswordUseCase: Get.find<ForgotPassword>(),
        resetPasswordUseCase: Get.find<ResetPassword>(),
        isAuthenticatedUseCase: Get.find<IsAuthenticated>(),
        changePasswordUseCase: Get.find<ChangePassword>(),
      ),
      fenix: true,
    );

    Get.lazyPut(
      () => HomeController(
        getAdsUseCase: Get.find<GetAds>(),
        getCurrentTripUseCase: Get.find<GetCurrentTrip>(),
        getProfileUseCase: Get.find<GetProfile>(),
      ),
      fenix: true,
    );

    Get.lazyPut(
      () => DashboardController(
        getDashboardStatsUseCase: Get.find<GetDashboardStats>(),
        getSchoolStatsUseCase: Get.find<GetSchoolStats>(),
        getRecentTripsUseCase: Get.find<GetRecentTrips>(),
        getUpcomingTripsUseCase: Get.find<GetUpcomingTrips>(),
      ),
      fenix: true,
    );

    Get.lazyPut(
      () => SupervisorsController(
        getSupervisorsUseCase: Get.find<GetSupervisorsUseCase>(),
        getSupervisorByIdUseCase: Get.find<GetSupervisorByIdUseCase>(),
        createSupervisorUseCase: Get.find<CreateSupervisorUseCase>(),
        updateSupervisorUseCase: Get.find<UpdateSupervisorUseCase>(),
        deleteSupervisorUseCase: Get.find<DeleteSupervisorUseCase>(),
        supervisorRepository: Get.find<SupervisorRepository>(),
      ),
      fenix: true,
    );

    Get.lazyPut(
      () => ParentsController(
        getParentsUseCase: Get.find<GetParentsUseCase>(),
        getParentByIdUseCase: Get.find<GetParentByIdUseCase>(),
        createParentUseCase: Get.find<CreateParentUseCase>(),
        updateParentUseCase: Get.find<UpdateParentUseCase>(),
        deleteParentUseCase: Get.find<DeleteParentUseCase>(),
        getStudentsByParentIdUseCase: Get.find<GetStudentsByParentIdUseCase>(),
      ),
      fenix: true,
    );

    Get.lazyPut(
      () => TripController(
        getCurrentTripUseCase: Get.find<GetCurrentTrip>(),
        getRecentTripsUseCase: Get.find<GetRecentTripsUseCase>(),
        getTripByIdUseCase: Get.find<GetTripById>(),
        createTripUseCase: Get.find<CreateTripUseCase>(),
        updateTripUseCase: Get.find<UpdateTripUseCase>(),
        deleteTripUseCase: Get.find<DeleteTripUseCase>(),
        startTripUseCase: Get.find<StartTripUseCase>(),
        endTripUseCase: Get.find<EndTripUseCase>(),
        getTripTrackingUseCase: Get.find<GetTripTrackingUseCase>(),
        getTripsUseCase: Get.find<GetTripsUseCase>(),
      ),
      fenix: true,
    );
  }
}
